import React, { useState } from 'react';

export default function App() {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [messages, setMessages] = useState([
    { id: 1, sender: 'bot', text: '您好！我是您的銀行客服助手，有什麼可以幫助您？' },
  ]);
  const [inputValue, setInputValue] = useState('');

  const handleSendMessage = async () => {
    if (inputValue.trim()) {
      const userMessage = { id: messages.length + 1, sender: 'user', text: inputValue };
      setMessages(prev => [...prev, userMessage]);

      try {
        const response = await fetch('http://localhost:5000/chat', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ message: inputValue })
        });

        const data = await response.json();
        const botMessage = {
          id: messages.length + 2,
          sender: 'bot',
          text: data.reply || '很抱歉，目前無法回覆。'
        };

        setMessages(prev => [...prev, botMessage]);
      } catch (error) {
        console.error('聊天錯誤:', error);
      }

      setInputValue('');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">客戶服務</h1>
          
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">常見問題</h2>
            <div className="space-y-4">
              {[
                "如何重設我的登入密碼？",
                "轉帳需要多少時間才會到帳？",
                "信用卡申請進度查詢方式？",
                "如何變更我的聯絡資訊？"
              ].map((question, index) => (
                <div key={index} className="border-b border-gray-200 pb-4">
                  <h3 className="font-medium text-gray-800">{question}</h3>
                  <p className="text-gray-600 mt-2">
                    答案內容：這是一個示範回答，說明如何解決此類問題。
                  </p>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">聯絡我們</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium text-gray-700 mb-2">電話客服</h3>
                <p className="text-gray-600">0800-123-456</p>
                <p className="text-sm text-gray-500">24小時服務專線</p>
              </div>
              <div>
                <h3 className="font-medium text-gray-700 mb-2">電子郵件</h3>
                <p className="text-gray-600"><EMAIL></p>
                <p className="text-sm text-gray-500">通常在24小時內回覆</p>
              </div>
              <div>
                <h3 className="font-medium text-gray-700 mb-2">營業時間</h3>
                <p className="text-gray-600">週一至週五 9:00 - 18:00</p>
                <p className="text-sm text-gray-500">例假日除外</p>
              </div>
              <div>
                <h3 className="font-medium text-gray-700 mb-2">分行地址</h3>
                <p className="text-gray-600">100台北市中正區南京東路一段1號</p>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Floating Chat Button */}
      <button 
        onClick={() => setIsChatOpen(!isChatOpen)}
        className="fixed bottom-6 right-6 bg-blue-600 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-lg hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-expanded={isChatOpen}
        aria-label="開啟客服聊天"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
        </svg>
      </button>

      {/* Chat Window */}
      {isChatOpen && (
        <div className="fixed bottom-20 right-6 w-80 bg-white rounded-lg shadow-xl overflow-hidden transform transition-all duration-300 ease-in-out animate-fadeIn">
          <div className="bg-blue-600 text-white p-3 flex justify-between items-center">
            <h3 className="font-semibold">線上客服</h3>
            <button 
              onClick={() => setIsChatOpen(false)}
              className="text-white hover:text-gray-200 focus:outline-none"
              aria-label="關閉客服聊天"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div className="h-80 overflow-y-auto p-3 space-y-3 bg-gray-50">
            {messages.map(message => (
              <div 
                key={message.id} 
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div 
                  className={`max-w-xs px-4 py-2 rounded-lg ${
                    message.sender === 'user' 
                      ? 'bg-blue-500 text-white rounded-br-none' 
                      : 'bg-white text-gray-800 rounded-bl-none shadow'
                  }`}
                >
                  {message.text}
                </div>
              </div>
            ))}
          </div>
          
          <div className="p-3 border-t border-gray-200 bg-white">
            <div className="flex space-x-2">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder="輸入訊息..."
                className="flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                onClick={handleSendMessage}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                送出
              </button>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out forwards;
        }
      `}</style>
    </div>
  );
}
